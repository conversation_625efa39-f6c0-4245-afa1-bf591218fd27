# 1. Use official Node.js image
FROM node:20-alpine AS builder

# 2. Set working directory
WORKDIR /app

# 3. Copy dependencies
COPY package.json pnpm-lock.yaml* ./
COPY . .

# 4. Install dependencies
RUN npm install -g pnpm && pnpm install

# 5. Build the Next.js app
RUN pnpm build

# 6. Production image
FROM node:20-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production
ENV PORT=3000

# 7. Copy only necessary files from builder
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next ./.next
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/next.config.mjs ./next.config.mjs

# 8. Start the app
EXPOSE 3000
CMD ["node_modules/.bin/next", "start"]
