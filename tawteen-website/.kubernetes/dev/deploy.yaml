apiVersion: apps/v1
kind: Deployment
metadata:
  name: tawteen-website
  namespace: tawteen-website 
  labels:
    app: tawteen-website 
spec:
  replicas: 2
  selector:
    matchLabels:
      app: tawteen-website 
  template:
    metadata:
      labels:
        app: tawteen-website 
    spec:
      containers:
        - name: tawteen-website  
          image: ${IMAGE_NAME}:${IMAGE_TAG}
          ports:
          - containerPort: 3000
          resources: {} 
      imagePullSecrets:
        - name: tawteen-website-secret
        
--- 
apiVersion: v1
kind: Service
metadata:
  name: tawteen-website-service 
  namespace: tawteen-website
  labels:
    app: tawteen-website
spec:
  selector:
    app: tawteen-website 
  ports:
    - protocol: TCP
      port: 80
      targetPort: 3000
