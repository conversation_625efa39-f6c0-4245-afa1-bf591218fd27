apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: vconnect-website-ingress
  namespace: tawteen-website 
  annotations: 
    cert-manager.io/cluster-issuer: letsencrypt-prod
spec:
  ingressClassName: nginx
  tls:
  - hosts:
    - tawteen.dragonteam.dev
    secretName: tawteen-website-cert-tls
  rules:
  - host: tawteen.dragonteam.dev
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name:  tawteen-website-service 
            port:
              number: 80 
