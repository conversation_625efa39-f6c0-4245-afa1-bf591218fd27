"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Calendar } from "lucide-react"
import { useLanguage } from "@/contexts/LanguageContext"

interface EventCardProps {
  title: string
  date: string
  description: string
  type: string
  isRTL?: boolean
}

export default function EventCard({ title, date, description, type, isRTL }: EventCardProps) {
  const { language } = useLanguage()

  return (
    <Card
      className={`bg-white hover:shadow-lg transition-shadow p-1 h-full flex flex-col ${isRTL ? "text-right" : ""}`}
    >
      <CardHeader className="pb-4">
        <div className={`flex items-center justify-between mb-3 ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className="text-sm text-blue-600 font-medium bg-blue-50 px-2 py-1 rounded">{type}</span>
          <div className={`flex items-center text-gray-500 ${isRTL ? "flex-row-reverse" : ""}`}>
            <Calendar className={`h-4 w-4 ${isRTL ? "ml-1" : "mr-1"}`} />
            <span className="text-sm">{date}</span>
          </div>
        </div>
        <CardTitle className="text-xl leading-tight">{title}</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col justify-between">
        <CardDescription className="text-gray-600 mb-6 flex-1 leading-relaxed">{description}</CardDescription>
        <Button
          variant="outline"
          className="w-full border-blue-600 text-blue-600 hover:bg-blue-50 bg-transparent mt-auto"
        >
          {language === "ar" ? "سجل الآن" : "Register Now"}
        </Button>
      </CardContent>
    </Card>
  )
}
