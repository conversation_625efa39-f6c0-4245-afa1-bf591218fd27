"use client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslation } from "@/hooks/useTranslation";
import toast from "react-hot-toast";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { MailTemplate24Filled } from "@fluentui/react-icons";

const StayUpdated = () => {
  const { t, isRTL } = useTranslation();

  const subscribeValidationSchema = Yup.object({
    email: Yup.string()
      .email(t.home.validationEmail)
      .required(t.home.requiredEmail),
  });

  const handleSubscribeSubmit = async (
    values: { email: string },
    { setSubmitting, resetForm }: any
  ) => {
    const headers = {
      Authorization: `token ${process.env.NEXT_PUBLIC_LEAD_API_TOKEN_FORM}`,
    };
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL_FORM}/api/resource/Newsletter Tawteen`,
        values,
        { headers }
      );
      if (response) {
        toast.success(
          isRTL ? "تم الاشتراك بنجاح!" : "Successfully subscribed!",
          { duration: 3000 }
        );
        resetForm();
        setSubmitting(false);
      }
    } catch (error) {
      toast.error(
        isRTL
          ? "يبدو أن هذا البريد الإلكتروني مشترك مسبقًا في النشرة الإخبارية. إذا كنت ترغب في تحديث بياناتك أو تغيير البريد، يُرجى استخدام بريد إلكتروني مختلف."
          : "Error subscribing to newsletter",
        { duration: 3000 }
      );
      setSubmitting(false);
    }
  };

  return (
    <section className="py-20 bg-primary">
      <div className="container mx-auto max-w-[1440px] px-3 lg:px-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight text-white sm:text-4xl">
            {t.home.subscribeTitle}
          </h2>
          <p className="mt-4 text-lg text-white opacity-90 text-[24px]">
            {t.home.subscribeDescription}
          </p>
          <p className="mt-4 text-lg text-white opacity-90 text-[24px]">
            {t.home.subscribeDescriptionTwo}
          </p>
        </div>

      <Formik
  initialValues={{
    email: "",
    company: "V.Connct Egypt",
  }}
  validationSchema={subscribeValidationSchema}
  onSubmit={handleSubscribeSubmit}
>
          {({ isSubmitting, errors, touched }) => (
            <Form>
              <div className="flex flex-col items-center">
                <div className="md:mx-auto w-full md:w-1/2 xl:w-[30%] bg-white flex items-center justify-between mt-10 border-[1px] border-borderColor rounded-full p-2">
                  <div className="flex items-center gap-2 w-full">
                    <MailTemplate24Filled className="text-primary" />
                    <p className="font-[700] text-[15px] whitespace-nowrap">
                      {t.home.subscribeWithUS}
                    </p>
                    <p>|</p>

                    {/* ✅ Email input */}
                    <Field
                      as={Input}
                      type="text"
                      name="email"
                      placeholder="<EMAIL>"
                      className={`bg-white placeholder:text-[11px] sm:placeholder:text-[15px] px-0 placeholder:text-gray-400 md:placeholder:text-[13px] lg:placeholder:text-[15px] focus:outline-none border-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full ${
                        isRTL ? "text-right" : ""
                      }`}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="font-[700] text-[16px] bg-primary hover:bg-hover text-white rounded-full px-3 md:px-5"
                  >
                    {t.home.subscribe}
                  </Button>
                </div>

                {/* ✅ Error message */}
                <ErrorMessage
                  name="email"
                  component="div"
                  className={`text-red-500 text-sm mt-1 ${isRTL ? "text-right" : ""}`}
                />
              </div>
            </Form>
          )}
        </Formik>
      </div>
    </section>
  );
};

export default StayUpdated;