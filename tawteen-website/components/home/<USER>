"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslation } from "@/hooks/useTranslation";
import toast from "react-hot-toast";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { MailTemplate24Filled } from "@fluentui/react-icons";
import Image from "next/image";

const HeroSection = () => {
  const { t, isRTL } = useTranslation();

  // ✅ Yup validation schema
  const subscribeValidationSchema = Yup.object({
    email: Yup.string()
      .email(t.home.validationEmail)
      .required(t.home.requiredEmail),
  });

  // ✅ Handle form submission
  const handleSubscribeSubmit = async (
    values: { email: string },
    { setSubmitting, resetForm }: any
  ) => {
    const headers = {
      Authorization: `token ${process.env.NEXT_PUBLIC_LEAD_API_TOKEN_FORM}`,
    };
    try {
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL_FORM}/api/resource/Newsletter Tawteen`,
        values,
        { headers }
      );
      if (response) {
        toast.success(
          isRTL ? "تم الاشتراك بنجاح!" : "Successfully subscribed!",
          { duration: 3000 }
        );
        resetForm();
        setSubmitting(false);
      }
    } catch (error) {
      toast.error(
        isRTL ? "يبدو أن هذا البريد الإلكتروني مشترك مسبقًا في النشرة الإخبارية.إذا كنت ترغب في تحديث بياناتك أو تغيير البريد، يُرجى استخدام بريد إلكتروني مختلف." : "Error subscribing to newsletter",
        { duration: 3000 }
      );
      setSubmitting(false);
    }
  };

  return (
    <section className="relative py-10 md:py-20">
      <div className="container mx-auto max-w-[1440px] px-2 md:px-6">
        <div className="text-center">
          <div className="flex items-center flex-col xl:flex-row gap-1 justify-center">
            <h1 className="text-4xl font-bold tracking-tight text-black sm:text-6xl lg:text-5xl">
              {t.home.heroHeader}
            </h1>
            <h1 className="text-4xl font-bold tracking-tight text-primary sm:text-6xl lg:text-5xl">
              {t.home.heroTitle}
            </h1>
          </div>

          <p className="w-full mt-6 text-lg leading-8 text-black sm:text-xl">
            {t.home.heroDescription}
          </p>

          {/* ✅ Formik form */}
          <Formik
            initialValues={{
              email: "",
              company: "V.Connct Egypt",
            }}
            validationSchema={subscribeValidationSchema}
            onSubmit={handleSubscribeSubmit}
          >
            {({ isSubmitting, errors, touched }) => (
              <Form>
                <div className="flex flex-col items-center">
                  <div className="md:mx-auto w-full md:w-1/2 xl:w-[30%] bg-white flex items-center justify-between mt-10 border-[1px] border-borderColor rounded-full p-2">
                    <div className="flex items-center gap-2 w-full">
                      <MailTemplate24Filled className="text-primary" />
                      <p className="font-[700] text-[15px] whitespace-nowrap">
                        {t.home.subscribeWithUS}
                      </p>
                      <p>|</p>

                      {/* ✅ Input field */}
                      <Field
                        as={Input}
                        type="text"
                        name="email"
                        placeholder="<EMAIL>"
                        className={`bg-white placeholder:text-[11px] sm:placeholder:text-[15px] px-0 placeholder:text-gray-400 md:placeholder:text-[13px] lg:placeholder:text-[15px] focus:outline-none border-none focus:ring-0 focus-visible:ring-0 focus-visible:ring-offset-0 w-full ${
                          isRTL ? "text-right" : ""
                        }`}
                      />
                    </div>

                    {/* Submit Button */}
                    <Button
                      type="submit"
                      disabled={isSubmitting}
                      className="font-[700] text-[16px] bg-primary hover:bg-hover text-white rounded-full px-3 md:px-5"
                    >
                      {t.home.subscribe}
                    </Button>
                  </div>

                  {/* Error message */}
                  <ErrorMessage
                    name="email"
                    component="div"
                    className={`text-red-500 text-sm mt-1 ${isRTL ? "text-right" : ""}`}
                  />
                </div>
              </Form>
            )}
          </Formik>

          {/* ✅ Image */}
          <div className="flex justify-center mt-5">
            <Image
              src="/images/Image.svg"
              alt="Image"
              width={800} // استخدم أبعاد مناسبة بدل 0
              height={400}
              priority
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default HeroSection;
