"use client ";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";
import Link from "next/link";
import { Button } from "../ui/button";

const AboutUs = () => {
  const { t, isRTL } = useTranslation();

  return (
    <section className="py-14">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16 items-center">
          <div className={isRTL ? "lg:order-2" : ""}>
            <Image
              src="/images/Home1 copy 2.svg"
              alt="Innovation in action"
              width={600}
              height={500}
            />
          </div>
          <div className={`${isRTL ? "lg:order-1 text-right" : ""}`}>
            <h2
              className={`text-2xl  sm:text-xl !text-[32px] ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.home.sectionTitle}
            </h2>
            <p
              className={`mt-6 text-lg leading-8 text-gray-600 ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.home.sectionDescription}
            </p>
            <div className={`mt-8 ${isRTL ? "text-right" : ""}`}>
              <Link href="/about">
                <Button className="bg-primary rounded-full hover:bg-hover text-white">
                  {t.home.sectionButton}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AboutUs;
