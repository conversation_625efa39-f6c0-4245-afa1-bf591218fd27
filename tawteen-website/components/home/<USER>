"use client ";
import { useTranslation } from "@/hooks/useTranslation";
import toast from "react-hot-toast";
import { Button } from "../ui/button";

const Logos = () => {
  const { t, isRTL } = useTranslation();
  const notify = () => {
    toast(isRTL ? " شيء جديد على وشك الانطلاق قريبا" : "Comming Soon", {
      duration: 2000,
    });
  };
  return (
    <section className="py-20 bg-gray-50 ">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold  sm:text-4xl">
            {t.home.companiesTitle}
          </h2>
          <p className="mt-4 text-lg ">{t.home.companiesSubtitle}</p>
        </div>

        <div className="mt-12 flex flex-col lg:flex-row gap-5">
          {/* right cards*/}
          <div className="flex flex-col gap-6 order-1 lg:-order-1 ">
            {/* Vezeeta Section - Top Right */} 
            <div className=" rounded-2xl p-8 flex-1 border-2 border-gray-200">
              <div className="flex items-center justify-center">
                <img
                  src="/images/Image (2).svg"
                  alt="Vezeeta"
                  className="w-full"
                />
              </div>
              <div className="flex flex-col gap-1 mt-4">
                <h1 className="font-bold text-[32px]">
                  {t.home.logoTwo.title}
                </h1>

                <p>{t.home.logoTwo.description}</p>
              </div>
            </div>

            {/* Noon Section - Bottom Right */}
            <div className=" rounded-2xl p-8 flex-1 border-2 border-gray-200">
              <div className="flex items-center justify-center">
                <img
                  src="/images/Image (3).svg"
                  alt="Vezeeta"
                  className="w-full"
                />
              </div>
              <div className="flex flex-col gap-1 mt-4">
                <h1 className="font-bold text-[32px]">
                  {t.home.logoThree.title}
                </h1>

                <p>{t.home.logoThree.description}</p>
              </div>
            </div>
          </div>
          {/* left cards */}
          <div className="bg-blueLight order-0 rounded-[16px] p-8  flex justify-center flex-col items-center border-[1px] border-borderColor  ">
            <img
              src="/images/Image (1).svg"
              alt="vconnct"
              className="rounded-xl w-full"
            />
            <div className="flex flex-col gap-1 mt-4">
              <h1 className="font-bold text-[32px]">{t.home.logoOne.title}</h1>

              <p>{t.home.logoOne.descOne}</p>
              <p>{t.home.logoOne.descTwo}</p>
              <p>{t.home.logoOne.descThree}</p>
            </div>
          </div>
        </div>
        <div className="mt-12 text-center">
          <Button
            onClick={notify}
            className="bg-primary hover:bg-hover text-white rounded-full "
          >
            {t.home.companiesButton}
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Logos;
