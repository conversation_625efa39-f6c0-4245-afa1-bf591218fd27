"use client ";
import { useTranslation } from "@/hooks/useTranslation";
import { Button } from "../ui/button";
import toast from "react-hot-toast";

const Events = () => {
  const { t, isRTL } = useTranslation();
  const notify = () => {
    toast(isRTL ? " شيء جديد على وشك الانطلاق قريبا" : "Comming Soon", {
      duration: 2000,
    });
  };
  return (
    <section className="py-12 lg:py-24 bg-gray-50">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight sm:text-4xl">
            {t.home.eventsTitle}
          </h2>
        </div>

        {/* <div className="grid grid-cols-1 gap-12  lg:gap-16 mb-8 items-center"> */}
        <div className="flex justify-center text-center mb-10">
          <p className="text-lg leading-relaxed text-gray-600">
            <span className="font-bold text-primary">
              {" "}
              {t.home.eventsSubtitle}{" "}
            </span>
            {t.home.eventDetails}
            <br />
            {t.home.eventDetails2}
          </p>
        </div>
        {/* </div> */}

        <div className=" flex flex-col lg:flex-row gap-8 items-stretch justify-center mb-10 lg:mb-16">
          <div className="flex flex-col gap-2">
            <img src="/images/event 3 1 (1).svg" alt="Event 3" />
            <div className=" text-white bg-primary  rounded-xl flex items-center justify-end gap-4 ">
              <p className="max-w-[300px] text-end">
                ACM Designing Interactive Systems Conference (DIS) 2025
              </p>
              <div className="flex flex-col  bg-borderColor rounded-l-xl py-2 px-4 items-start ">
                <span className="mr-1">29</span>
                <span>JUL</span>
              </div>
            </div>
          </div>

          <div className="flex flex-col gap-2">
            <img src="/images/event 2 1.svg" alt="Event 2" />
            <div className=" text-white bg-primary  rounded-xl flex items-center justify-end gap-4 ">
              <p>Momentum AI San Jose 2025</p>
              <div className="flex flex-col  bg-borderColor rounded-l-xl py-2 px-4 items-start ">
                <span className="mr-1">15</span>
                <span>JUL</span>
              </div>
            </div>
          </div>
          <div className="flex flex-col gap-2">
            <img src="/images/event-1-01 1.svg" alt="Event 1" />
            <div className=" text-white bg-primary  rounded-xl flex items-center justify-end gap-4 ">
              <p>CompTIA ChannelCon 2025</p>
              <div className="flex flex-col  bg-borderColor rounded-l-xl py-2 px-4 items-start ">
                <span className="mr-1">29</span>
                <span>JUL</span>
              </div>
            </div>
          </div>
        </div>

        <div className="text-center">
          <Button
            size="lg"
            onClick={notify}
            className="bg-primary hover:bg-hover text-white px-5 py-5 text-[15px] font-semibold rounded-full shadow-lg hover:shadow-xl transition-all duration-200"
          >
            {t.home.viewAllEvents}
          </Button>
        </div>
      </div>
    </section>
  );
};

export default Events;
