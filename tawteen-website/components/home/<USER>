"use client ";
import { useTranslation } from "@/hooks/useTranslation";
import {
  DataHistogram24Filled,
  DocumentTextFilled,
  SearchRegular,
} from "@fluentui/react-icons";
import toast from "react-hot-toast";

const Resources = () => {
  const { t, isRTL } = useTranslation();
  const notify = () => {
    toast(isRTL ? " شيء جديد على وشك الانطلاق قريبا" : "Comming Soon", {
      duration: 2000,
    });
  };

  return (
    <section className="py-14">
      <div className="max-w-[1440px] mx-auto px-4 text-right" dir="rtl">
        <h1 className="text-4xl font-bold mb-4 text-center">
          {t.home.resourcesTitle}
        </h1>

        <p className="text-gray-700 mb-10 text-center leading-relaxed">
          {t.home.resourcesDescription}
        </p>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10 w-[90%] lg:w-full mx-auto">
          <div className="bg-blue-50 rounded-[16px] border-[1px] border-borderColor  p-8 flex flex-col items-center text-center">
            <SearchRegular className="text-primary h-8 w-8 mb-4" />
            <h3 className="text-lg font-semibold text-black mb-2">
              {t.home.firstCard}
            </h3>
          </div>

          <div className="bg-blue-50 rounded-[16px] border-[1px] border-borderColor  p-8 flex flex-col items-center text-center">
            <DocumentTextFilled className="text-navy-700 h-8 w-8 mb-4 text-primary" />
            <h3 className="text-lg font-semibold text-black mb-2">
              {t.home.secondCard}
            </h3>
          </div>

          <div className="bg-blue-50 rounded-[16px] border-[1px] border-borderColor  p-8 flex flex-col items-center text-center">
            <DataHistogram24Filled className="text-navy-700 h-8 w-8 mb-4 text-primary" />
            <h3 className="text-lg font-semibold text-black mb-2">
              {t.home.thirdCard}
            </h3>
          </div>
        </div>
        <div className="text-center">
          <button
            onClick={notify}
            className="bg-primary hover:bg-hover text-white  px-6 py-3 rounded-full font-medium hover:bg-navy-800 transition-colors"
          >
            {t.home.startRead}
          </button>
        </div>
      </div>
    </section>
  );
};

export default Resources;
