"use client";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { useTranslation } from "@/hooks/useTranslation";
import { useState } from "react";
import toast from "react-hot-toast";

export default function ContactForm2() {
  const { isRTL } = useTranslation();
  const [formData, setFormData] = useState({
    name: "",
    companyName: "",
    phone: "",
    partnershipType: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    toast.success(isRTL ? "تم إرسال الطلب بنجاح" : "Request sent successfully");
    setFormData({ name: "", companyName: "", phone: "", partnershipType: "" });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <section className="py-16 px-6 bg-gray-50">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            كيف يمكننا التعاون؟
          </h2>
        </div>

        {/* Contact Form Card */}
        <Card className="bg-white border-2 border-blue-200 rounded-3xl shadow-lg max-w-2xl mx-auto">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl font-bold text-gray-900">
              طلب الشراكة
            </CardTitle>
          </CardHeader>
          <CardContent className="px-8 pb-8">
            <form onSubmit={handleSubmit} className="space-y-6">
                {/* Name Field */}
              <div className="relative">
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="الاسم"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right placeholder-gray-400 bg-gray-50"
                  required
                />
              </div>

              {/* Company Name Field */}
              <div className="relative">
                <input
                  type="text"
                  name="companyName"
                  value={formData.companyName}
                  onChange={handleInputChange}
                  placeholder="اسم الشركة"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right placeholder-gray-400 bg-gray-50"
                  required
                />
              </div>

              {/* Phone Field */}
              <div className="relative">
                <input
                  type="tel"
                  name="phone"
                  value={formData.phone}
                  onChange={handleInputChange}
                  placeholder="رقم سبب التواصل"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right placeholder-gray-400 bg-gray-50"
                  required
                />
              </div>

              {/* Partnership Type Field */}
              <div className="relative">
                <input
                  type="text"
                  name="partnershipType"
                  value={formData.partnershipType}
                  onChange={handleInputChange}
                  placeholder="نوع الشراكة التي تهتم بها"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-right placeholder-gray-400 bg-gray-50"
                  required
                />
              </div>

              {/* Submit Button */}
              <div className="text-center pt-4">
                <Button
                  type="submit"
                  className="bg-blue-900 hover:bg-blue-800 text-white rounded-full px-12 py-4 text-lg font-semibold"
                >
                  إرسال الطلب
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
