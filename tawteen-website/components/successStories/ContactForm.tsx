"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import Image from "next/image";
import { useTranslation } from "@/hooks/useTranslation";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import axios from "axios";
import toast from "react-hot-toast";
import { useEffect } from "react";

const ContactForm = () => {
  const { t, isRTL } = useTranslation();
  const contactValidationSchema = Yup.object({
    full_name: Yup.string()
      .min(2, t.contact.validationFullName)
      .required(t.contact.requiredName),
    email: Yup.string()
      .email(t.contact.validationEmail)
      .required(t.contact.requiredEmail),
    reason_for_contact: Yup.string().required(t.contact.contactReason),
    message: Yup.string().when("reason_for_contact", {
      is: "Other",
      then: (schema) =>
        schema
          .min(10, t.contact.validationMessage)
          .required(t.contact.requiredMessage),
      otherwise: (schema) => schema.notRequired(),
    }),
  });
  // Contact reasons options
  const contactReasons = [
    {
      value: "Strategic Partnership",
      label: isRTL ? "شراكة استراتيجية" : "Strategic Partnership",
    },
    {
      value: "Initiative Information Request",
      label: isRTL
        ? "طلب معلومات عن المبادرة"
        : "Initiative Information Request",
    },
    {
      value: "Media Collaboration Request",
      label: isRTL ? "طلب تعاون إعلامي" : "Media Collaboration Request",
    },
    {
      value: "Funding and Development Opportunities",
      label: isRTL
        ? "فرص تمويل وتطوير"
        : "Funding and Development Opportunities",
    },
    {
      value: "Other",
      label: isRTL ? "أخرى" : "Other",
    },
  ];

  // Handle form submission
  const handleContactSubmit = async (
    values: {
      full_name: string;
      email: string;
      reason_for_contact: string;
      message: string;
    },
    { setSubmitting, resetForm }: any
  ) => {
    const headers = {
      Authorization: `token ${process.env.NEXT_PUBLIC_LEAD_API_TOKEN_FORM}`,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL_FORM}/api/resource/Contact Us Tawteen`,
        values,
        { headers }
      );

      if (response) {
        toast.success(
          isRTL ? "تم إرسال رسالتك بنجاح!" : "Message sent successfully!",
          {
            duration: 3000,
          }
        );
        resetForm();
      }
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error(
        isRTL ? "يبدو أن هذا البريد الإلكتروني مشترك مسبقًا في النشرة الإخبارية.إذا كنت ترغب في تحديث بياناتك أو تغيير البريد، يُرجى استخدام بريد إلكتروني مختلف." : "It seems that this email address is already subscribed to the newsletter. If you'd like to update your information or change the email, please use a different email address.",
        {
          duration: 3000,
        }
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <section className="lg:py-20">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16  h-full">
          {/* Left Side - Image */}
          <div className={isRTL ? "lg:order-2" : ""}>
            <Image
              src="/images/Fill form copy 1.svg"  
              alt="Contact us - Customer support and communication"
              width={0}
              height={0}
              className="rounded-2xl w-full"
            />
          </div>

          {/* Right Side - Contact Form */}
          <div
            className={`p-8 rounded-2xl border-[1px] border-borderColor bg-[#FBFDFF] ${
              isRTL ? "lg:order-1 text-right" : ""
            }`}
          >
            <h2
              className={`text-3xl font-bold text-black mb-6 text-center
              `}
            >
              {t.contact.formTitle}
            </h2>

            <Formik
              initialValues={{
                full_name: "",
                email: "",
                reason_for_contact: "",
                message: "",
                company: "V.Connct Egypt",
              }}
              validationSchema={contactValidationSchema}
              onSubmit={handleContactSubmit}
            >
              {({ isSubmitting, errors, touched, setFieldValue, values }) => {
                // Clear message when contactReason changes from "other"
                useEffect(() => {
                  if (values.reason_for_contact !== "other") {
                    setFieldValue("message", "");
                  }
                }, [values.reason_for_contact, setFieldValue]);

                return (
                  <>
                    <Form className="space-y-6 flex flex-col gap-10">
                      
                      <div className="flex flex-col gap-8">
                        <div className="space-y-2">
                          <Field
                            as={Input}
                            id="full_name"
                            name="full_name"
                            type="text"
                            placeholder={t.contact.fullNamePlaceholder}
                            className={`w-full px-4 py-3 border border-borderColor rounded-full  placeholder:text-gray-400 min-h-[50px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                              isRTL ? "text-right" : ""
                            } ${
                              errors.full_name && touched.full_name
                                ? "border-red-500 focus:border-red-500"
                                : ""
                            }`}
                          />
                          <ErrorMessage
                            name="full_name"
                            component="div"
                            className={`text-red-500 text-sm mt-1 ${
                              isRTL ? "text-right" : ""
                            }`}
                          />
                        </div>

                        <div className="space-y-2">
                          <Field
                            as={Input}
                            id="email"
                            name="email"
                            type="email"
                            placeholder={t.contact.emailPlaceholder}
                            className={`w-full px-4 py-3 border border-borderColor rounded-full  placeholder:text-gray-400 min-h-[50px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${
                              isRTL ? "text-right" : ""
                            } ${
                              errors.email && touched.email
                                ? "border-red-500 focus:border-red-500"
                                : ""
                            }`}
                          />
                          <ErrorMessage
                            name="email"
                            component="div"
                            className={`text-red-500 text-sm mt-1 ${
                              isRTL ? "text-right" : ""
                            }`}
                          />
                        </div>

                        <div className="space-y-2">
                          <Select
                            value={values.reason_for_contact}
                            onValueChange={(value) =>
                              setFieldValue("reason_for_contact", value)
                            }
                            dir={isRTL ? "rtl" : "ltr"}
                          >
                            <SelectTrigger
                              className={`w-full px-4 py-3 border border-borderColor rounded-full  placeholder:text-gray-400 min-h-[50px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors text-gray-400 bg-white ${
                                isRTL ? "text-right" : ""
                              } ${
                                errors.reason_for_contact &&
                                touched.reason_for_contact
                                  ? "border-red-500 focus:border-red-500"
                                  : ""
                              }`}
                            >
                              <SelectValue
                                placeholder={
                                  isRTL
                                    ? "اختر سبب التواصل"
                                    : "Select contact reason"
                                }
                              />
                            </SelectTrigger>
                            <SelectContent className="bg-white  cursor-pointer border-borderColor rounded-xl ">
                              {contactReasons.map((reason) => (
                                <SelectItem
                                  className="   cursor-pointer hover:!bg-blueLight"
                                  key={reason.value}
                                  value={reason.value}
                                >
                                  {reason.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <ErrorMessage
                            name="reason_for_contact"
                            component="div"
                            className={`text-red-500 text-sm mt-1 ${
                              isRTL ? "text-right" : ""
                            }`}
                          />
                        </div>

                        {values.reason_for_contact === "Other" && (
                          <div className="space-y-2">
                            <Field
                              as={Textarea}
                              id="message"
                              name="message"
                              placeholder={
                                isRTL
                                  ? "يرجى توضيح سبب التواصل..."
                                  : "Please specify your contact reason..."
                              }
                              rows={5}
                              className={`w-full px-4 py-3 border border-borderColor rounded-xl  placeholder:text-gray-400 min-h-[50px]rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                                isRTL ? "text-right" : ""
                              } ${
                                errors.message && touched.message
                                  ? "border-red-500 focus:border-red-500"
                                  : ""
                              }`}
                            />
                            <ErrorMessage
                              name="message"
                              component="div"
                              className={`text-red-500 text-sm mt-1 ${
                                isRTL ? "text-right" : ""
                              }`}
                            />
                          </div>
                        )}
                      </div>

                      <div>
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          className={`w-full bg-primary hover:bg-hover rounded-full text-white py-6 px-6 text-lg  font-medium transition-colors ${
                            isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                        >
                          {isSubmitting
                            ? isRTL
                              ? "جاري الإرسال..."
                              : "Sending..."
                            : t.contact.sendButton}
                        </Button>
                      </div>
                    </Form>
                  </>
                );
              }}
            </Formik>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ContactForm;
