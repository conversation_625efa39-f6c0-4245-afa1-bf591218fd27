"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";

export default function CompanyCards() {
  const { t, isRTL } = useTranslation();

  return (
    <section className="py-16 px-6 bg-white">
      <div className="container mx-auto max-w-6xl">
        {/* Company Cards */}
        <div className="grid md:grid-cols-2 gap-8 mb-12">
          {/* Noon Card */}
          <Card className="bg-white border-2 border-gray-200 hover:shadow-lg transition-shadow rounded-2xl overflow-hidden">
            <CardHeader className="text-center p-0">
              <div className="bg-blue-600 text-white py-12 flex items-center justify-center">
                <Image
                  src="/images/Image (3).svg"
                  alt="Vezeeta"
                  width={200}
                  height={80}
                  className="object-contain"
                />
              </div>
            </CardHeader>
            <CardContent className="p-8 text-center">
              <CardTitle className="text-2xl font-bold text-gray-900 mb-4">
                Noon
              </CardTitle>
              <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                منصة عربية رقمية لحجز واستشارات الأطباء، تُسهّل الوصول إلى الرعاية الصحية في 
                مصر والمنطقة والمختصة
              </p>
              <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-8 py-3">
                زيارة الموقع
              </Button>
            </CardContent>
          </Card>

          {/* Vezeeta Card */}
          <Card className="bg-white border-2 border-gray-200 hover:shadow-lg transition-shadow rounded-2xl overflow-hidden">
            <CardHeader className="text-center p-0">
              <div className="bg-yellow-400 text-black py-12 flex items-center justify-center">
                <Image
                  src="/images/Image (2).svg"
                  alt="Noon"
                  width={200}
                  height={80}
                  className="object-contain"
                />
              </div>
            </CardHeader>
            <CardContent className="p-8 text-center">
              <CardTitle className="text-2xl font-bold text-gray-900 mb-4">
                Vezteeta
              </CardTitle>
              <p className="text-gray-600 mb-6 leading-relaxed text-lg">
                منصة عربية رائدة في التجارة الإلكترونية، تُظهر كيف يمكن للتكنولوجيا أن تُطوّر محليًا 
                وتخدم احتياجات السوق العربي
              </p>
              <Button className="bg-yellow-500 hover:bg-yellow-600 text-black rounded-full px-8 py-3 font-semibold">
                زيارة الموقع
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Center Button */}
        <div className="text-center">
          <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-12 py-4 text-lg font-semibold">
            رؤية المزيد
          </Button>
        </div>
      </div>
    </section>
  );
}
