"use client";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useTranslation } from "@/hooks/useTranslation";
import toast from "react-hot-toast";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import axios from "axios";
import { MailTemplate24Filled } from "@fluentui/react-icons";
import Image from "next/image";

const Hero = () => {
  const { t, isRTL } = useTranslation();



  return (
    <section className="relative py-10 md:py-20">
      <div className="container mx-auto max-w-[1440px] px-2 md:px-6">
        <div className="text-center">
          <div className="flex items-center flex-col xl:flex-row gap-1 justify-center">
            <h1 className="text-4xl font-bold tracking-tight text-black sm:text-6xl lg:text-5xl">
              {t.digitalSovereignty.title}
            </h1>
            
          </div>

          <p className="w-full mt-6 text-lg leading-8 text-black sm:text-xl">
            {t.digitalSovereignty.subtitle}
          </p>

          

          {/* ✅ Image */}
          <div className="flex justify-center mt-5">
            <Image
              src="/images/success.svg"
              alt="Image"
              width={800} // استخدم أبعاد مناسبة بدل 0
              height={400}
              priority
              className="w-full h-auto"
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;
