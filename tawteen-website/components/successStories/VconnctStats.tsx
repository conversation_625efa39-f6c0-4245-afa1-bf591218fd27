"use client";

import { Button } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";

export default function VconnctStats() {
  const { isRTL, t } = useTranslation();

  return (
    <section className="py-16 px-6 bg-gray-50">
      <div className="container mx-auto max-w-6xl">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16 h-full">
  {/* Left side - Text content */}
  <div className="lg:order-1">
    {/* Your text content goes here */}
    <p>This should appear on the left on large screens</p>
  </div>

  {/* Right side - Image and Stats */}
  <div className="lg:order-2">
    <div className="bg-white rounded-xl shadow-lg border-2 border-gray-200 overflow-hidden">
      {/* Platform Image on top */}
      <div className="aspect-video bg-white rounded-t-xl flex items-center justify-center p-6">
        <Image
          src="/images/Image(4).svg"
          alt="Vconnct Platform"
          width={400}
          height={250}
          className="object-contain w-full h-full"
        />
      </div>

      {/* Stats below image */}
      <div className="p-8">
        <div className="grid grid-cols-2 gap-8">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900 mb-2">250 ألف مستخدم</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900 mb-2">120 دولة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900 mb-2">اكثر من 50 جلسة</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-900 mb-2">150 الف تحميل</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

          {/* Right side - Content */}
          <div className={`order-1 lg:order-2 ${isRTL ? "text-right" : "text-left"}`}>
            <div className="mb-6">
              <div className="flex gap-3 mb-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-gray-50 text-blue-900 border-gray-200 hover:bg-blue-100"
                >
                  قصص نجاح عربية
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-gray-50 text-blue-900 border-gray-200 hover:bg-gray-100"
                >
                  الإنجازات المحققة
                </Button>
              </div>
              
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                منصة اجتماعات (مؤتمرات الفيديو) 
                Vconnct عربية
              </h2>
              
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                {t.digitalSovereignty.VconnctStats.description}
              </p>
            </div>

            <div className="flex gap-4">
              <Button 
                className="bg-blue-900 hover:bg-blue-700 text-white rounded-full px-8 py-3"
              >
                {t.digitalSovereignty.VconnctStats.buttonText}
              </Button>
              <Button 
                variant="outline"
                className="border-blue-900 text-blue-600 hover:bg-blue-50 rounded-full px-8 py-3"
              >
                {t.digitalSovereignty.VconnctStats.buttonTexts}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
