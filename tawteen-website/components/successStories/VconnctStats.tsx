"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";

export default function VconnctStats() {
  const { t, isRTL } = useTranslation();

  const stats = [
    {
      number: "250",
      unit: "ألف مستخدم",
      label: "250 ألف مستخدم"
    },
    {
      number: "120",
      unit: "دولة",
      label: "120 دولة"
    },
    {
      number: "50",
      unit: "أكثر من",
      label: "أكثر من 50 جلسة",
      subLabel: "150 ألف تحميل"
    }
  ];

  return (
    <section className="py-16 px-6 bg-gray-50">
      <div className="container mx-auto max-w-6xl">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* Left side - Image placeholder */}
          <div className="order-2 lg:order-1">
            <Card className="bg-white border-2 border-gray-200 rounded-2xl overflow-hidden">
              <CardContent className="p-0">
                {/* Platform Image */}
                <div className="aspect-video bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center p-8">
                  <Image
                    src="/images/Image(4).svg"
                    alt="Vconnct Platform"
                    width={400}
                    height={300}
                    className="object-contain w-full h-full"
                  />
                </div>
                
                {/* Stats overlay */}
                <div className="relative -mt-20 mx-6 mb-6">
                  <div className="bg-white rounded-xl shadow-lg p-6">
                    <div className="grid grid-cols-2 gap-6">
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">250</div>
                        <div className="text-sm text-gray-600">ألف مستخدم</div>
                      </div>
                      <div className="text-center">
                        <div className="text-2xl font-bold text-gray-900">120</div>
                        <div className="text-sm text-gray-600">دولة</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900">أكثر من 50</div>
                        <div className="text-xs text-gray-600">جلسة</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-gray-900">150</div>
                        <div className="text-xs text-gray-600">ألف تحميل</div>
                      </div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right side - Content */}
          <div className={`order-1 lg:order-2 ${isRTL ? "text-right" : "text-left"}`}>
            <div className="mb-6">
              <div className="flex gap-3 mb-4">
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-blue-50 text-blue-600 border-blue-200 hover:bg-blue-100"
                >
                  قصص نجاح عربية
                </Button>
                <Button 
                  variant="outline" 
                  size="sm" 
                  className="bg-gray-50 text-gray-600 border-gray-200 hover:bg-gray-100"
                >
                  الإنجازات المحققة
                </Button>
              </div>
              
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-6 leading-tight">
                منصة اجتماعات (مؤتمرات الفيديو) 
                <span className="text-blue-600">Vconnct</span> عربية
              </h2>
              
              <p className="text-lg text-gray-600 leading-relaxed mb-8">
                المؤتمر التقني الأول في المنطقة يجمع أكثر من 500 خبير من 
                العالم والمختصين وصناع القرار المساهمين في تشكيل 
                مستقبل منصومة التكنولوجيا العربية
              </p>
            </div>

            <div className="flex gap-4">
              <Button 
                className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-8 py-3"
              >
                زيارة الموقع
              </Button>
              <Button 
                variant="outline"
                className="border-blue-600 text-blue-600 hover:bg-blue-50 rounded-full px-8 py-3"
              >
                رؤية المزيد
              </Button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
