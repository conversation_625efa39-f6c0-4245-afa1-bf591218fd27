"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import axios from "axios";
import toast from "react-hot-toast";

const ContactForm2 = () => {
  const contactValidationSchema = Yup.object({
    full_name: Yup.string()
      .min(2, "الاسم يجب أن يكون أكثر من حرفين")
      .required("الاسم مطلوب"),
    company_name: Yup.string()
      .min(2, "اسم الشركة مطلوب")
      .required("اسم الشركة مطلوب"),
    phone: Yup.string()
      .min(10, "رقم الهاتف يجب أن يكون صحيحاً")
      .required("رقم الهاتف مطلوب"),
    partnership_type: Yup.string()
      .min(5, "نوع الشراكة مطلوب")
      .required("نوع الشراكة مطلوب"),
  });

  // Handle form submission
  const handleContactSubmit = async (
    values: {
      full_name: string;
      company_name: string;
      phone: string;
      partnership_type: string;
    },
    { setSubmitting, resetForm }: any
  ) => {
    const headers = {
      Authorization: `token ${process.env.NEXT_PUBLIC_LEAD_API_TOKEN_FORM}`,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL_FORM}/api/resource/Contact Us Tawteen`,
        values,
        { headers }
      );

      if (response) {
        toast.success("تم إرسال طلبك بنجاح!", {
          duration: 3000,
        });
        resetForm();
      }
    } catch (error) {
      console.error("Error sending message:", error);
      toast.error("حدث خطأ أثناء إرسال الطلب. يرجى المحاولة مرة أخرى.", {
        duration: 3000,
      });
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <section className="py-16 px-6 bg-white">
      <div className="container mx-auto max-w-4xl">
        <div className="text-center mb-12">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">
            كيف يمكننا التعاون؟
          </h1>
        </div>
        
        <div className="max-w-2xl mx-auto">
          <div className="p-8 rounded-2xl border-2 border-blue-200 bg-white">
            <h2 className="text-2xl font-bold text-gray-900 mb-8 text-center">
              طلب الشراكة
            </h2>

            <Formik
              initialValues={{
                full_name: "",
                company_name: "",
                phone: "",
                partnership_type: "",
              }}
              validationSchema={contactValidationSchema}
              onSubmit={handleContactSubmit}
            >
              {({ isSubmitting, errors, touched }) => {
                return (
                  <>
                    <Form className="space-y-6">
                      <div className="flex flex-col gap-6">
                        <div className="space-y-2">
                          <Field
                            as={Input}
                            id="full_name"
                            name="full_name"
                            type="text"
                            placeholder="الاسم"
                            className={`w-full px-6 py-4 border-2 border-blue-200 rounded-full placeholder:text-gray-400 text-right min-h-[56px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-gray-50 ${
                              errors.full_name && touched.full_name
                                ? "border-red-500 focus:border-red-500"
                                : ""
                            }`}
                          />
                          <ErrorMessage
                            name="full_name"
                            component="div"
                            className="text-red-500 text-sm mt-1 text-right"
                          />
                        </div>

                        <div className="space-y-2">
                          <Field
                            as={Input}
                            id="company_name"
                            name="company_name"
                            type="text"
                            placeholder="اسم الشركة"
                            className={`w-full px-6 py-4 border-2 border-blue-200 rounded-full placeholder:text-gray-400 text-right min-h-[56px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-gray-50 ${
                              errors.company_name && touched.company_name
                                ? "border-red-500 focus:border-red-500"
                                : ""
                            }`}
                          />
                          <ErrorMessage
                            name="company_name"
                            component="div"
                            className="text-red-500 text-sm mt-1 text-right"
                          />
                        </div>

                        <div className="space-y-2">
                          <Field
                            as={Input}
                            id="phone"
                            name="phone"
                            type="tel"
                            placeholder="رقم سبب التواصل"
                            className={`w-full px-6 py-4 border-2 border-blue-200 rounded-full placeholder:text-gray-400 text-right min-h-[56px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-gray-50 ${
                              errors.phone && touched.phone
                                ? "border-red-500 focus:border-red-500"
                                : ""
                            }`}
                          />
                          <ErrorMessage
                            name="phone"
                            component="div"
                            className="text-red-500 text-sm mt-1 text-right"
                          />
                        </div>

                        <div className="space-y-2">
                          <Field
                            as={Input}
                            id="partnership_type"
                            name="partnership_type"
                            type="text"
                            placeholder="نوع الشراكة التي تهتم بها"
                            className={`w-full px-6 py-4 border-2 border-blue-200 rounded-full placeholder:text-gray-400 text-right min-h-[56px] focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors bg-gray-50 ${
                              errors.partnership_type && touched.partnership_type
                                ? "border-red-500 focus:border-red-500"
                                : ""
                            }`}
                          />
                          <ErrorMessage
                            name="partnership_type"
                            component="div"
                            className="text-red-500 text-sm mt-1 text-right"
                          />
                        </div>
                      </div>

                      <div className="text-center mt-8">
                        <Button
                          type="submit"
                          disabled={isSubmitting}
                          className={`bg-blue-900 hover:bg-blue-800 text-white rounded-full px-12 py-4 text-lg font-medium transition-colors ${
                            isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                        >
                          {isSubmitting ? "جاري الإرسال..." : "إرسال الطلب"}
                        </Button>
                      </div>
                    </Form>
                  </>
                );
              }}
            </Formik>
          </div>
        </div>
      </div>
    </section>
  );
};

"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import toast from "react-hot-toast";

export default function ContactForm2() {
  const [formData, setFormData] = useState({
    name: "",
    companyName: "",
    email: "",
    partnershipType: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    toast.success("تم إرسال الطلب بنجاح");
    setFormData({ name: "", companyName: "", email: "", partnershipType: "" });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <section className="py-16 px-6 bg-white">
      <div className="container mx-auto max-w-2xl">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold text-gray-900 mb-8">
            كيف يمكننا التعاون؟
          </h2>
        </div>

        <Card className="bg-white border-2 border-blue-200 rounded-3xl shadow-lg">
          <CardHeader className="text-center pb-6">
            <CardTitle className="text-2xl font-bold text-gray-900">
              طلب الشراكة
            </CardTitle>
          </CardHeader>
          <CardContent className="px-8 pb-8">
            <form onSubmit={handleSubmit} className="space-y-6">
              <div>
                <input
                  type="text"
                  name="name"
                  value={formData.name}
                  onChange={handleInputChange}
                  placeholder="الاسم"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full bg-gray-50 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  required
                />
              </div>

              <div>
                <input
                  type="text"
                  name="companyName"
                  value={formData.companyName}
                  onChange={handleInputChange}
                  placeholder="اسم الشركة"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full bg-gray-50 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  required
                />
              </div>

              <div>
                <input
                  type="email"
                  name="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="ادخل بريد التواصل"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full bg-gray-50 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  required
                />
              </div>

              <div>
                <input
                  type="text"
                  name="partnershipType"
                  value={formData.partnershipType}
                  onChange={handleInputChange}
                  placeholder="نوع الشراكة التي تهتم بها"
                  className="w-full px-6 py-4 border-2 border-blue-200 rounded-full bg-gray-50 text-gray-900 placeholder-gray-500 focus:ring-2 focus:ring-blue-500 focus:border-transparent text-right"
                  required
                />
              </div>

              <div className="text-center pt-4">
                <Button
                  type="submit"
                  className="bg-blue-900 hover:bg-blue-800 text-white rounded-full px-12 py-4 text-lg font-semibold"
                >
                  إرسال الطلب
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
export default ContactForm2;
