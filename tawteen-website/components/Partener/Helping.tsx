import { useTranslation } from "@/hooks/useTranslation";
import { Card, CardContent } from "../ui/card";
import Link from "next/link";
import { Button } from "../ui/button";

const Helping = () => {
    const { t, isRTL } = useTranslation();
    return(
         <section className="py-2 lg:pb-14">
                <div className="container mx-auto max-w-[1440px] px-6">
                  <div className="text-center mb-8 lg:mb-10">
                    <h2 className="text-3xl font-bold tracking-tight text-black sm:text-4xl lg:text-5xl">
                      {t.partner.whatYouGetTitle}
                    </h2>
                
                  </div>
        
                  <div className="flex flex-wrap gap-[32px] justify-around">
                    {[
                      {
                      
                        text: t.partner.dedicatedSupportText,
                        color: "blue",
                      },
                      {
                       
                        text: t.partner.trainingResourcesText,
                        color: "green",
                      },
                      {
                 
                        text: t.partner.revenueOpportunitiesText,
                        color: "purple",
                      },
                    ].map((item, index) => (
                      <Card
                        key={index}
                        className={`${index == 0 ? "w-full lg:w-[96%]" : "w-full lg:w-[45%]"} bg-blueLight p-[24px] rounded-xl text-center  border-borderColor border-[1px]  ${
                          isRTL ? "text-right" : ""
                        }`}
                      >
                
                        <CardContent className="p-0">
                          <p className="text-black text-base leading-relaxed text-center">
                            {item.text}
                          </p>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                   <div className="w-full flex justify-center mt-10">
            <Link href="#partenerFormr" className=" mx-auto">
                <Button className="bg-primary rounded-full hover:bg-hover text-white">
                  {t.partner.title}
                </Button>
              </Link>
          </div>
                </div>
              </section>
    )
}

export default Helping;