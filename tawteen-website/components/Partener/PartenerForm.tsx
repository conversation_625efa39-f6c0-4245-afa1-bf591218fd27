
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

import { useTranslation } from "@/hooks/useTranslation";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import axios from "axios";
import toast from "react-hot-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect } from "react";
import Image from "next/image";
const PartenerForm = () => {
      const { t, isRTL } = useTranslation();

  // Partnership type options
  const partnershipTypes = [
    {
      value: "Financial Partner",
      label: isRTL ? "شريك مالي" : "Financial Partner",
    },
    {
      value: "Technical Partnership",
      label: isRTL ? "شراكة تقنية" : "Technical Partnership",
    },
    {
      value: "Strategic Partnership",
      label: isRTL ? "شراكة استراتيجية" : "Strategic Partnership",
    },
    {
      value: "Marketing Support",
      label: isRTL ? "دعم تسويقي" : "Marketing Support",
    },
    {
      value: "Other",
      label: isRTL ? "أخرى" : "Other",
    },
  ];

   const partnerValidationSchema = Yup.object({
    full_name: Yup.string()
      .min(2, isRTL ? "يجب أن يكون الاسم مكونًا من حرفين على الأقل" : "Name must be at least 2 characters")
      .required(isRTL ? "الاسم  مطلوب" : "Full name is required"),
    company_name: Yup.string()
      .min(2, isRTL ? "يجب أن يكون اسم الشركة مكونًا من حرفين على الأقل" : "Company name must be at least 2 characters")
      .required(isRTL ? "اسم الشركة مطلوب" : "Company name is required"),
    business_email: Yup.string()
      .email(isRTL ? "عنوان بريد إلكتروني غير صحيح" : "Invalid email address")
      .required(isRTL ? "البريد الإلكتروني  مطلوب" : "Business email is required"),
    partnership_type_of_interest: Yup.string().required(
      isRTL ? "نوع الشراكة مطلوب" : "Partnership type is required"
    ),
    message: Yup.string().when("partnership_type_of_interest", {
      is: "Other",
      then: (schema) =>
        schema
          .min(10, isRTL ? "يجب أن تكون الرسالة مكونة من 10 أحرف على الأقل" : "Message must be at least 10 characters")
          .required(isRTL ? "الرسالة مطلوبة" : "Message is required"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  // Handle form submission
  const handlePartnerSubmit = async (
    values: {
      full_name: string;
      company_name: string;
      business_email: string;
      partnership_type_of_interest: string;
      message: string;
    },
    { setSubmitting, resetForm }: any
  ) => {
    const headers = {
      Authorization: `token ${process.env.NEXT_PUBLIC_LEAD_API_TOKEN_FORM}`,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL_FORM}/api/resource/Partnership Application Tawteen`,
        values,
        { headers }
      );

      if (response) {
        toast.success(
          isRTL
            ? "تم إرسال طلب الشراكة بنجاح!"
            : "Partnership application submitted successfully!",
          {
            duration: 3000,
          }
        );
        resetForm();
      }
    } catch (error) {
      console.error("Error submitting partner application:", error);
      toast.error(
        isRTL ? "يبدو أن هذا البريد الإلكتروني مشترك مسبقًا في النشرة الإخبارية.إذا كنت ترغب في تحديث بياناتك أو تغيير البريد، يُرجى استخدام بريد إلكتروني مختلف." : "It seems that this email address is already subscribed to the newsletter. If you'd like to update your information or change the email, please use a different email address.",
        {
          duration: 3000,
        }
      );
    } finally {
      setSubmitting(false);
    }
}


    return(
    <section className="py-10 scroll-offset" id="partenerFormr">
           
     <div className="container mx-auto max-w-[1440px] px-6">
            <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16  h-full">
              {/* Left Side - Image */}
              <div className={isRTL ? "" : ""}>
                <Image
                  src="/images/Become a partner 2 copy 2.svg"
                  alt="Contact us - Customer support and communication"
                  width={0}
                  height={0}
                  className="rounded-2xl w-full"
                />
              </div>
            {/* Registration Form */}
            <div className="">
              <div
                className={`bg-blueLight border-borderColor border-[1px] p-8 rounded-xl h-full ${
                  isRTL ? "text-right" : ""
                }`}
              >
                <h3 className="text-3xl font-bold text-black mb-6 text-center">
                  {t.partner.applicationTitle}
                </h3>
    
                <Formik
                  initialValues={{
                    full_name: "",
                    company_name: "",
                    business_email: "",
                    partnership_type_of_interest: "",
                    message: "",
                    company: "V.Connct Egypt",
                  }}
                  validationSchema={partnerValidationSchema}
                  onSubmit={handlePartnerSubmit}
                >
                  {({ isSubmitting, errors, touched, setFieldValue, values }) => {
                    useEffect(() => {
                      if (values.partnership_type_of_interest !== "other") {
                        setFieldValue("inquiry", "");
                      }
                    }, [values.partnership_type_of_interest, setFieldValue]);
    
                    return (
                      <Form  className="space-y-6  ">
                       <div className="flex flex-col gap-8">
                         <div className="space-y-2">
                          
                          <Field
                            as={Input}
                            id="full_name"
                            name="full_name"
                            type="text"
                            placeholder={t.partner.fullNamePlaceholder}
                            className={`w-full px-4 py-3 border border-borderColor rounded-full min-h-[50px]  placeholder:text-gray-400 min-h-[50px]rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                                isRTL ? "text-right" : ""
                              } ${
                                errors.full_name && touched.full_name
                                  ? "border-red-500 focus:border-red-500"
                                  : ""
                              }`}
                          />
                          <ErrorMessage
                            name="full_name"
                            component="div"
                            className={`text-red-500 text-sm mt-1 ${
                              isRTL ? "text-right" : ""
                            }`}
                          />
                        </div>
    
                        <div className="space-y-2">
                          
                          <Field
                            as={Input}
                            id="companyName"
                            name="company_name"
                            type="text"
                            placeholder={t.partner.companyNamePlaceholder}
                            className={`w-full px-4 py-3 border border-borderColor rounded-full min-h-[50px]  placeholder:text-gray-400 min-h-[50px]rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                                isRTL ? "text-right" : ""
                              } ${
                                errors.company_name && touched.company_name
                                  ? "border-red-500 focus:border-red-500"
                                  : ""
                              }`}
                          />
                          <ErrorMessage
                            name="company_name"
                            component="div"
                            className={`text-red-500 text-sm mt-1 ${
                              isRTL ? "text-right" : ""
                            }`}
                          />
                        </div>
    
                        <div className="space-y-2">
                         
                          <Field
                            as={Input}
                            id="businessEmail"
                            name="business_email"
                            type="email"
                            placeholder={t.partner.businessEmailPlaceholder}
                            className={`w-full px-4 py-3 border border-borderColor rounded-full min-h-[50px]  placeholder:text-gray-400 min-h-[50px]rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                                isRTL ? "text-right" : ""
                              } ${
                                errors.business_email && touched.business_email
                                  ? "border-red-500 focus:border-red-500"
                                  : ""
                              }`}
                          />
                          <ErrorMessage
                            name="business_email"
                            component="div"
                            className={`text-red-500 text-sm mt-1 ${
                              isRTL ? "text-right" : ""
                            }`}
                          />
                        </div>
    
                        <div className="space-y-2">
                         
                          <Select
                          dir="rtl"
                            value={values.partnership_type_of_interest}
                            onValueChange={(value) =>
                              setFieldValue("partnership_type_of_interest", value)
                            }
                          >
                            <SelectTrigger
                              className={`w-full px-4 py-3 border border-borderColor rounded-full min-h-[50px]  text-gray-400 min-h-[50px]rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                                isRTL ? "text-right" : ""
                              } ${
                                errors.partnership_type_of_interest && touched.partnership_type_of_interest
                                  ? "border-red-500 focus:border-red-500"
                                  : ""
                              }`}
                            >
                              <SelectValue
                                placeholder={
                                  isRTL
                                    ? "نوع الشراكة التي تهتم بها"
                                    : "Select partnership type"
                                }
                              />
                            </SelectTrigger>
                            <SelectContent className="bg-white  cursor-pointer border-borderColor rounded-xl">
                              {partnershipTypes.map((type) => (
                                <SelectItem
                                  className="cursor-pointer hover:!bg-blueLight"
                                  key={type.value}
                                  value={type.value}
                                >
                                  {type.label}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <ErrorMessage
                            name="partnership_type_of_interest"
                            component="div"
                            className={`text-red-500 text-sm mt-1 ${
                              isRTL ? "text-right" : ""
                            }`}
                          />
                        </div>
    
                        {values.partnership_type_of_interest === "Other" && (
                          <div className="space-y-2">
                           
                            <Field
                              as={Textarea}
                              id="message"
                              name="message"
                              placeholder={
                                isRTL
                                  ? "يرجى توضيح نوع الشراكة المطلوبة..."
                                  : "Please specify the type of partnership you're interested in..."
                              }
                              rows={5}
                             className={`w-full px-4 py-3 border border-borderColor rounded-full min-h-[50px]  placeholder:text-gray-400 min-h-[50px]rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors resize-none ${
                                isRTL ? "text-right" : ""
                              } ${
                                errors.message && touched.message
                                  ? "border-red-500 focus:border-red-500"
                                  : ""
                              }`}
                            />
                            <ErrorMessage
                              name="message"
                              component="div"
                              className={`text-red-500 text-sm mt-1 ${
                                isRTL ? "text-right" : ""
                              }`}
                            />
                          </div>
                        )}
                       </div>
    
                          <Button
                          type="submit"
                          disabled={isSubmitting}
                          className={`w-full bg-primary hover:bg-hover rounded-full text-white py-6 px-6 text-lg  font-medium transition-colors ${
                            isSubmitting ? "opacity-50 cursor-not-allowed" : ""
                          }`}
                        >
                          {isSubmitting
                            ? isRTL
                              ? "جاري الإرسال..."
                              : "Sending..."
                            : t.partner.submitButton}
                        </Button>
                      </Form>
                    );
                  }}
                </Formik>
              </div>
            </div>
            </div>
            </div>
          </section>
    )
}

export default PartenerForm;