import { useTranslation } from "@/hooks/useTranslation";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Button } from "../ui/button";
const WhyPartner = () =>{
    const { t , isRTL } = useTranslation();
    return(
         <section className="lg:py-14 ">
        <div className="container mx-auto max-w-[1440px] px-6">
          <div className="text-center lg:mb-16 mb-8">
            <h2 className="text-3xl font-bold tracking-tight text-black sm:text-4xl lg:text-5xl">
              {t.partner.whyPartnerTitle}
            </h2>
          </div>

          <div className="grid grid-cols-1 gap-8 md:grid-cols-2 lg:grid-cols-4 w-full">
            {[
              {
             
                text: t.partner.marketLeadershipText,
              },
              {
                
                text: t.partner.globalReachText,
              },
              {
                
                text: t.partner.innovationFocusText,
              },
              {
               
                text: t.partner.trustedPartnershipText,
              },
            ].map((item, index) => (
              <Card
                key={index}
                className={`bg-blueLight  border-borderColor border-[1px] rounded-xl flex items-start justify-center px-[32px] py-[24px] ${
                  isRTL ? "text-right" : ""
                }`}
              >
               
                <CardContent className="text-center p-0">
                   <p> {item.text}</p>
                </CardContent>
              </Card>
            ))}
              
          </div>
          <div className="w-full flex justify-center mt-10">
            <Link href="#partenerFormr" className=" mx-auto">
                <Button className="bg-primary rounded-full hover:bg-hover text-white">
                  {t.partner.title}
                </Button>
              </Link>
          </div>
        </div>
      </section>
    )
}
export default WhyPartner;