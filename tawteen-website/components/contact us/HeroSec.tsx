"use client ";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";

const HeroSec = () => {
  const { t } = useTranslation();
  return (
    <section className="py-10 ">
      <div className="container mx-auto max-w-[1440px] px-6 flex flex-col gap-3">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight text-black sm:text-6xl lg:text-5xl">
            {t.contact.title}
          </h1>
          <p className="mx-auto mt-6 w-full text-lg leading-8 text-black sm:text-xl ">
            {t.contact.description}
          </p>
        </div>
        <div className="flex flex-col justify-center items-center">
          <Image
            priority
            src="/images/Contact us 1.svg"
            alt="Contact us"
            width={0}
            height={0}
            className="w-full h-auto"
          />
        </div>
      </div>
    </section>
  );
};

export default HeroSec;
