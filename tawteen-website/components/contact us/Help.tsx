import { useTranslation } from "@/hooks/useTranslation";
import { But<PERSON> } from "../ui/button";

const Help = () => {
  const { t } = useTranslation();

  return (
    <section className="pb-14">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="text-center">
          <h2 className="text-3xl font-bold tracking-tight text-black sm:text-4xl lg:text-5xl mb-6">
            {t.contact.urgentTitle}
          </h2>
          <p className="text-lg leading-8 text-black mb-8">
            {t.contact.urgentDescription}
          </p>

          <div className="bg-blue-50 border-[1px] border-borderColor rounded-xl p-6 w-full">
            <p className="text-lg text-gray-700 w-full">
              <a href="mailto:<EMAIL>">
             <span className=" text-primary text-2xl font-bold">
                <EMAIL>
              </span>
          </a>
            
            </p>
          </div>

          {/* ✅ زر يفتح إيميل */}
          <a href="mailto:<EMAIL>">
            <Button className="bg-primary rounded-full mt-8 hover:bg-hover text-white">
              {t.contact.contactUsTitle}
            </Button>
          </a>
        </div>
      </div>
    </section>
  );
};

export default Help;
