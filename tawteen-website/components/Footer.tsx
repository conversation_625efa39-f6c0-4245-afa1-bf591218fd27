"use client";

import Link from "next/link";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuSub,
  DropdownMenuSubTrigger,
  DropdownMenuSubContent,
} from "@/components/ui/dropdown-menu";
import { Button } from "@/components/ui/button";

export default function Footer() {
  const { t } = useTranslation();

  return (
    <footer className="bg-gray-50 py-8 px-6">
      <div className="max-w-7xl mx-auto">
        {/* Main content section */}
        <div className="flex flex-col lg:flex-row items-center lg:items-start justify-center gap-3 lg:gap-8 ite  mb-8">
          {/* Logo */}
          <Link href="/" className={`flex items-center }`}>
            <div className="flex-shrink-0">
              <Image
                src="/images/logo.svg"
                width={150}
                height={100}
                alt="logo"
              />
            </div>
          </Link>

          {/* Arabic text content */}
          <div className="flex-1 text-right max-w-[70%] ">
            <p className="text-primary text-md leading-relaxed">
              {t.footer.description}
            </p>
          </div>
        </div>

        {/* Navigation links */}
        <nav className="mb-8">
          <ul className="flex flex-wrap justify-center gap-3 lg:gap-8 text-sm text-gray-600">
            <li>
              <Link href="/" className="text-primary  text-lg">
                {t.nav.home}
              </Link>
            </li>
            <li>
              <Link href="/about" className="text-primary  text-lg">
                {t.nav.aboutUS}
              </Link>
            </li>
            <li>
              <Link href="/partner" className="text-primary  text-lg">
                {t.nav.partner}
              </Link>
            </li>
            <li>
              <Link href="/contact" className="text-primary  text-lg">
                {t.footer.contactUs}
              </Link>
            </li>
            {/* <li>
              <Link href="#" className="text-primary  text-lg">
                {t.footer.check}
              </Link>
            </li>
            <li>
              <Link href="#" className="text-primary  text-lg">
                {t.footer.updates}
              </Link>
            </li>
            <li>
              <Link href="#" className="text-primary  text-lg">
                {t.footer.commonQuick}
              </Link>
            </li>
            <li>
              <Link href="#" className="text-primary  text-lg">
                {t.footer.tawteen}
              </Link>
            </li>
            <li>
              <Link href="#" className="text-primary  text-lg">
                {t.footer.bePaertener}
              </Link>
            </li>
            <li>
              <Link href="#" className="text-primary  text-lg">
                {t.footer.aboutUs}
              </Link>
            </li> */}
          </ul>
        </nav>














        {/* Social media icons */}
     <div className="flex justify-center gap-4">
  <Link
    href="https://www.facebook.com/TawteenTech"
    target="_blank"
    className="w-10 h-10 border-hover border-[1px] rounded-full flex items-center justify-center text-primary text-lg transition-colors shadow-sm"
    aria-label="Facebook"
  >
    <Image
      src="/images/facebook.svg"
      width={12}
      height={12}
      alt="Facebook"
    />
  </Link>

  <Link
    href="https://www.youtube.com/@TawteenTech"
    target="_blank"
    className="w-10 h-10 border-hover border-[1px] rounded-full flex items-center justify-center text-primary text-lg transition-colors shadow-sm "
    aria-label="YouTube"
  >
    <Image
      src="/images/youtube.svg"
      width={20}
      height={20}
      alt="YouTube"
    />
  </Link>

  <Link
    href="https://www.linkedin.com/company/tawteen-tech-eg/"
    target="_blank"
    className="w-10 h-10 border-hover border-[1px] rounded-full flex items-center justify-center text-primary text-lg transition-colors shadow-sm"
    aria-label="LinkedIn"
  >
    <Image
      src="/images/linked.svg"
      width={20}
      height={20}
      alt="LinkedIn"
    />
  </Link>

  <Link
    href="https://x.com/TawteenTech"
    target="_blank"
    className="w-10 h-10 border-hover border-[1px] rounded-full flex items-center justify-center text-primary text-lg transition-colors shadow-sm"
    aria-label="Twitter"
  >
    <Image
      src="/images/twiter.svg"
      width={20}
      height={20}
      alt="Twitter"
    />
  </Link>

  <Link
    href="https://www.instagram.com/tawteentech/"
    target="_blank"
    className="w-10 h-10 border-hover border-[1px] rounded-full flex items-center justify-center text-primary text-lg transition-colors shadow-sm"
    aria-label="Instagram"
  >
    <Image
      src="/images/insta.svg"
      width={20}
      height={20}
      alt="Instagram"
    />
  </Link>
</div>

      </div>
    </footer>
  );
}
