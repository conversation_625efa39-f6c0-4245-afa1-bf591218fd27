"use client"

import { <PERSON>, Card<PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { ArrowRight } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useLanguage } from "@/contexts/LanguageContext"

interface ResourceCardProps {
  title: string
  description: string
  category: string
  imageUrl?: string
  isRTL?: boolean
}

export default function ResourceCard({ title, description, category, imageUrl, isRTL }: ResourceCardProps) {
  const { language } = useLanguage()

  return (
    <Card className={`hover:shadow-lg transition-shadow p-1 h-full flex flex-col ${isRTL ? "text-right" : ""}`}>
      <CardHeader className="pb-4">
        <Image
          src={imageUrl || `/placeholder.svg?height=200&width=300`}
          alt={title}
          width={300}
          height={200}
          className="rounded-lg mb-4 w-full object-cover"
        />
        <div className={`flex items-center justify-between mb-2 ${isRTL ? "flex-row-reverse" : ""}`}>
          <span className="text-sm text-blue-600 font-medium">{category}</span>
        </div>
        <CardTitle className="text-xl leading-tight">{title}</CardTitle>
      </CardHeader>
      <CardContent className="flex-1 flex flex-col justify-between">
        <CardDescription className="text-gray-600 mb-4 flex-1">{description}</CardDescription>
        <Link
          href="#"
          className={`text-blue-600 hover:text-blue-700 font-medium inline-flex items-center mt-auto ${isRTL ? "flex-row-reverse" : ""}`}
        >
          {language === "ar" ? "اقرأ المزيد" : "Read More"}
          <ArrowRight className={`h-4 w-4 ${isRTL ? "mr-1 rotate-180" : "ml-1"}`} />
        </Link>
      </CardContent>
    </Card>
  )
}
