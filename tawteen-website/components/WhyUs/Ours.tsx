"use client ";
import {
  <PERSON>,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  CardTitle,
} from "../ui/card";
import { useTranslation } from "@/hooks/useTranslation";

const Ours = () => {
  const { t, isRTL } = useTranslation();
  return (
    <section className=" lg:py-14 mx-auto max-w-[1370px]">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
          {/* Mission Card */}
          <Card
            className={`bg-white border-[1px] border-borderColor rounded-[12px] ${
              isRTL ? "text-right" : ""
            }`}
          >
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-2xl font-bold text-black">
                {t.about.mission}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-black text-base leading-relaxed">
                {t.about.missionText}
              </CardDescription>
            </CardContent>
          </Card>

          {/* Vision Card */}
          <Card
            className={`bg-white border-[1px] border-borderColor rounded-[12px] ${
              isRTL ? "text-right" : ""
            }`}
          >
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-2xl font-bold text-black">
                {t.about.vision}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-black text-base leading-relaxed">
                {t.about.visionText}
              </CardDescription>
            </CardContent>
          </Card>

          {/* Values Card */}
          <Card
            className={`bg-white border-[1px] border-borderColor rounded-[12px] ${
              isRTL ? "text-right" : ""
            }`}
          >
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-2xl font-bold text-black">
                {t.about.values}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription className="text-black text-base leading-relaxed ">
                {t.about.valuesText}
              </CardDescription>
            </CardContent>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default Ours;
