"use client ";
import Image from "next/image";

import { useTranslation } from "@/hooks/useTranslation";

const WhoUS = () => {
  const { t, isRTL } = useTranslation();
  return (
    <section className="py-14">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16 items-center">
          <div className={`${isRTL ? "lg:order-1 text-right" : ""}`}>
            <h2
              className={`text-2xl  font-bold !text-[32px] ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.about.title}
            </h2>
            <p
              className={`mt-6 text-lg leading-8 text-gray-600 ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.about.descriptionOne}
            </p>
            <p
              className={` text-lg leading-8 text-gray-600 ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.about.descriptionTwo}
            </p>
          </div>
          <div className={`mx-auto ${isRTL ? "lg:order-2" : ""}`}>
            <Image
              src="/images/Who are we copy 1.svg"
              alt="Innovation in action"
              width={600}
              height={500}
            />
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhoUS;
