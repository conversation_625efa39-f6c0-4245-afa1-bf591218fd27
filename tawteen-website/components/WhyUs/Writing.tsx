"use client ";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";
import Link from "next/link";
import { Button } from "../ui/button";

const Writing = () => {
  const { t, isRTL } = useTranslation();
  return (
    <section className="py-14">
      <div className="container mx-auto max-w-[1440px] px-6">
        <div className="grid grid-cols-1 gap-12 lg:grid-cols-2 lg:gap-16 items-center">
          <div className={`mx-auto `}>
            <Image
              src="/images/Who do we write for copy 1.svg"
              alt="Innovation in action"
              width={600}
              height={500}
            />
          </div>
          <div>
            <h2
              className={`text-2xl  font-bold !text-[32px] ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.about.writtenTitle}
            </h2>
            <p
              className={`mt-6 text-lg leading-8 text-gray-600 ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.about.writtenDescription}
            </p>
            {/* <p
              className={` text-lg leading-8 text-gray-600 ${
                isRTL ? "text-right" : ""
              }`}
            >
              {t.about.descriptionTwo}
            </p> */}
            <div className={`mt-8 ${isRTL ? "text-right" : ""}`}>
              <Link href="/partner">
                <Button className="bg-primary rounded-full hover:bg-hover text-white">
                  {t.about.writtenButton}
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Writing;
