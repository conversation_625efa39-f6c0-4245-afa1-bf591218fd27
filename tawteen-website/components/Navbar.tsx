"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Menu, X } from "lucide-react";
import Link from "next/link";
import { Suspense, useState } from "react";
import { useTranslation } from "@/hooks/useTranslation";
import Image from "next/image";

// Import the correct components
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // For mobile

import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card"; // For desktop
import { ChevronDownRegular } from "@fluentui/react-icons";

function Navbar() {
  const { t, isRTL } = useTranslation();
  // We only need state for the mobile menu now
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const toggleMobileMenu = () => {
    setIsMobileMenuOpen(!isMobileMenuOpen);
  };

  return (
    <nav
      className="sticky top-0 z-50 bg-white border-b border-gray-200 shadow-sm"
      dir={isRTL ? "rtl" : "ltr"}
    >
      <div className="container mx-auto max-w-[1440px] px-6">
        <div
          className={`flex items-center justify-between h-16 ${
            isRTL ? "flex-row-reverse" : ""
          }`}
        >
          {/* Get Started Button */}
          <Link href="/partner">
            <Button className="bg-primary hover:bg-hover text-white hidden md:inline-flex rounded-full">
              {t.nav.getStarted}
            </Button>
          </Link>

          {/* Desktop Navigation Links */}
          <div className="hidden md:block">
            <div
              className={`flex items-baseline space-x-8 ${
                isRTL ? "space-x-reverse" : ""
              }`}
            >
              <Link
                href="/"
                className="text-black font-bold hover:text-hover px-3 py-2 text-lg  transition-colors"
              >
                {t.nav.home}
              </Link>

              {/* Use HoverCard for Desktop */}
              <HoverCard openDelay={100} closeDelay={100}>
                <HoverCardTrigger asChild>
                  <Button
                    variant="outline"
                    className="text-black hover:text-hover px-3 py-2 text-lg font-bold transition-colors border-none"
                  >
                    {t.nav.about}
                  </Button>
                </HoverCardTrigger>
                <HoverCardContent
                  className="bg-white p-2 min-w-[200px] rounded-xl border-[0.5px] border-[#9c9c9c] w-auto"
                  sideOffset={5}
                  align="start"
                >
                  <div className="flex flex-col space-y-2">
                    <Link
                      href="/about"
                      className="block p-3 bg-[#F9F9F9] rounded-xl border border-transparent hover:border-borderColor hover:bg-blueLight {
 transition-all duration-75"
                    >
                      {t.nav.aboutUS}
                    </Link>
                    <Link
                      href="/partner"
                      className="block p-3 bg-[#F9F9F9] rounded-xl border border-transparent hover:border-borderColor hover:border-[1px] hover:bg-blueLight {
 transition-all duration-75"
                    >
                      {t.nav.partner}
                    </Link>
                  </div>
                </HoverCardContent>
              </HoverCard>

              <Link
                href="/digital-sovereignty"
                className="text-black hover:text-hover px-3 py-2 text-lg font-bold transition-colors"
              >
                {t.nav.digitalSovereignty}
              </Link>

              <Link
                href="/contact"
                className="text-black hover:text-hover px-3 py-2 text-lg font-bold transition-colors"
              >
                {t.nav.contact}
              </Link>
            </div>
          </div>

          {/* Logo and Mobile Menu Toggle */}
          <div
            className={`flex items-center justify-between md:w-auto w-full ${
              isRTL ? "space-x-reverse" : ""
            }`}
          >
            <Link
              href="/"
              className={`flex items-center ${isRTL ? "flex-row-reverse" : ""}`}
            >
              <div className="flex-shrink-0">
                <Image
                  src="/images/logo.svg"
                  width={100}
                  height={100}
                  alt="logo"
                />
              </div>
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="md:hidden"
              onClick={toggleMobileMenu}
              aria-label="Toggle mobile menu"
            >
              {isMobileMenuOpen ? (
                <X className="h-5 w-5" />
              ) : (
                <Menu className="h-5 w-5" />
              )}
            </Button>
          </div>
        </div>

        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
              <Link
                href="/"
                className="text-black hover:text-hover block px-3 py-2 text-lg font-bold transition-colors"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  setIsDropdownOpen(false);
                }}
              >
                {t.nav.home}
              </Link>

              <DropdownMenu
                open={isDropdownOpen}
                onOpenChange={setIsDropdownOpen}
              >
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    className="text-black hover:text-hover px-3 py-2 text-lg font-bold transition-colors border-none"
                  >
                    {t.nav.about}
                    <ChevronDownRegular />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent
                  className=" bg-white p-1 py-2 min-w-[200px] rounded-xl text-right border-[0.5px] border-[#9c9c9c] mr-2"
                  sideOffset={5}
                  align="start"
                >
                  <DropdownMenuItem
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      setIsDropdownOpen(false);
                    }}
                    className="flex justify-end border border-transparent hover:border-borderColor mt-0 bg-[#F9F9F9] rounded-xl hover:bg-blueLight {
 transition-all duration-75 cursor-pointer"
                    asChild
                  >
                    <Link href="/about" className="w-full block p-3">
                      {t.nav.aboutUS}
                    </Link>
                  </DropdownMenuItem>

                  <DropdownMenuItem
                    onClick={() => {
                      setIsMobileMenuOpen(false);
                      setIsDropdownOpen(false);
                    }}
                    className="flex justify-end border border-transparent hover:border-borderColor mt-3 bg-[#F9F9F9] rounded-xl hover:bg-blueLight {
 transition-all duration-75 cursor-pointer"
                    asChild
                  >
                    <Link href="/partner" className="w-full block p-3">
                      {t.nav.partner}
                    </Link>
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>

              <Link
                href="/digital-sovereignty"
                className="text-black hover:text-hover block px-3 py-2 text-lg font-bold transition-colors"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  setIsDropdownOpen(false);
                }}
              >
                {t.nav.digitalSovereignty}
              </Link>

              <Link
                href="/contact"
                className="text-black hover:text-hover block px-3 py-2 text-lg font-bold transition-colors"
                onClick={() => {
                  setIsMobileMenuOpen(false);
                  setIsDropdownOpen(false);
                }}
              >
                {t.nav.contact}
              </Link>
              <div className="pt-4 pb-2">
                <Link
                  href="/partner"
                  onClick={() => {
                    setIsMobileMenuOpen(false);
                    setIsDropdownOpen(false);
                  }}
                >
                  <Button className="bg-primary hover:bg-hover text-white rounded-full w-full">
                    {t.nav.getStarted}
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </nav>
  );
}

export default Navbar;
