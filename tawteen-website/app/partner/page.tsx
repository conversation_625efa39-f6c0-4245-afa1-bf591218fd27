"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";

import { useTranslation } from "@/hooks/useTranslation";
import { Formik, Form, Field, ErrorMessage } from "formik";
import * as Yup from "yup";
import axios from "axios";
import toast from "react-hot-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useEffect } from "react";
import PartenerHeroSection from "@/components/Partener/HerosSection";
import WhyPartner from "@/components/Partener/WhyPartner";
import Helping from "@/components/Partener/Helping";
import PartenerForm from "@/components/Partener/PartenerForm";
import StayUpdated from "@/components/home/<USER>";

// Validation schema

export default function PartnerPage() {
  const { t, isRTL } = useTranslation();

  // Partnership type options
  const partnershipTypes = [
    {
      value: "Financial Partner",
      label: isRTL ? "شريك مالي" : "Financial Partner",
    },
    {
      value: "Technical Partnership",
      label: isRTL ? "شراكة تقنية" : "Technical Partnership",
    },
    {
      value: "Strategic Partnership",
      label: isRTL ? "شراكة استراتيجية" : "Strategic Partnership",
    },
    {
      value: "Marketing Support",
      label: isRTL ? "دعم تسويقي" : "Marketing Support",
    },
    {
      value: "Other",
      label: isRTL ? "أخرى" : "Other",
    },
  ];

  const partnerValidationSchema = Yup.object({
    full_name: Yup.string()
      .min(2, "Name must be at least 2 characters")
      .required("Full name is required"),
    company_name: Yup.string()
      .min(2, "Company name must be at least 2 characters")
      .required("Company name is required"),
    business_email: Yup.string()
      .email("Invalid email address")
      .required("Business email is required"),
    partnership_type_of_interest: Yup.string().required(
      "Partnership type is required"
    ),
    message: Yup.string().when("partnership_type_of_interest", {
      is: "Other",
      then: (schema) =>
        schema
          .min(10, "Message must be at least 10 characters")
          .required("Message is required"),
      otherwise: (schema) => schema.notRequired(),
    }),
  });

  // Handle form submission
  const handlePartnerSubmit = async (
    values: {
      full_name: string;
      company_name: string;
      business_email: string;
      partnership_type_of_interest: string;
      message: string;
    },
    { setSubmitting, resetForm }: any
  ) => {
    const headers = {
      Authorization: `token ${process.env.NEXT_PUBLIC_LEAD_API_TOKEN_FORM}`,
    };

    try {
      setSubmitting(true);
      const response = await axios.post(
        `${process.env.NEXT_PUBLIC_API_URL_FORM}/api/resource/Partnership Application Tawteen`,
        values,
        { headers }
      );

      if (response) {
        toast.success(
          isRTL
            ? "تم إرسال طلب الشراكة بنجاح!"
            : "Partnership application submitted successfully!",
          {
            duration: 3000,
          }
        );
        resetForm();
      }
    } catch (error) {
      console.error("Error submitting partner application:", error);
      toast.error(
        isRTL ? "حدث خطأ أثناء إرسال الطلب" : "Error submitting application",
        {
          duration: 3000,
        }
      );
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white" dir={isRTL ? "rtl" : "ltr"}>
    
<PartenerHeroSection/>
     
<WhyPartner/>
     <Helping/>

     <PartenerForm/>
     <StayUpdated/>
    </div>
  );
}
