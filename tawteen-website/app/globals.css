@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}
body{

  scroll-behavior: smooth;
}

.scroll-offset {
  scroll-margin-top: 80px; 
}
/* Arabic font support using system fonts */

[dir="rtl"] .space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Custom RTL utilities */
.rtl\:space-x-reverse > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
}

/* Ensure proper text alignment for Arabic */
[dir="rtl"] input::placeholder {
  text-align: right;
}

[dir="rtl"] textarea::placeholder {
  text-align: right;
}

/* Fix for dropdown positioning in RTL */
[dir="rtl"] .absolute.right-0 {
  left: 0;
  right: auto;
}

/* Better Arabic text rendering */
[dir="rtl"] {
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Ensure proper spacing for Arabic text */
[dir="rtl"] p,
[dir="rtl"] h1,
[dir="rtl"] h2,
[dir="rtl"] h3,
[dir="rtl"] h4,
[dir="rtl"] h5,
[dir="rtl"] h6 {
  line-height: 1.6;
}

/* RTL Grid Layout Fixes */
[dir="rtl"] .lg\:order-1 {
  order: 1;
}

[dir="rtl"] .lg\:order-2 {
  order: 2;
}

/* Ensure centered text stays centered in RTL */
[dir="rtl"] .text-center {
  text-align: center !important;
}

/* Button spacing fixes for RTL */
[dir="rtl"] .space-x-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 1;
  margin-right: calc(1rem * var(--tw-space-x-reverse));
  margin-left: calc(1rem * calc(1 - var(--tw-space-x-reverse)));
}

/* Flex row reverse for RTL buttons */
[dir="rtl"] .sm\:flex-row-reverse {
  flex-direction: row-reverse;
}
.bg-image{
  background-image: url('/images/Become a Partner.jpg');
  background-size: 100% 100%;
  background-position: inherit;  
  background-position: center;  
  background-repeat: no-repeat;
}