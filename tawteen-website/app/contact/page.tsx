"use client";

import { useTranslation } from "@/hooks/useTranslation";

import HeroSec from "@/components/contact us/HeroSec";
import ContactForm from "@/components/contact us/ContactForm2";
import StayUpdated from "@/components/home/<USER>";
import Help from "@/components/contact us/Help";

export default function ContactPage() {
  const { t, isRTL } = useTranslation();

  return (
    <div className="min-h-screen bg-white" dir={isRTL ? "rtl" : "ltr"}>
      <HeroSec />
      <ContactForm />
      <Help />
      <StayUpdated />
    </div>
  );
}
