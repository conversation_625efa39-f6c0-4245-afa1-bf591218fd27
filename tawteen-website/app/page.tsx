"use client";
import { useTranslation } from "@/hooks/useTranslation";

import HeroSection from "@/components/home/<USER>";
import AboutUs from "@/components/home/<USER>";
import Logos from "@/components/home/<USER>";
import Resources from "@/components/home/<USER>";
import Events from "@/components/home/<USER>";
import StayUpdated from "@/components/home/<USER>";

export default function HomePage() {
  const { isRTL } = useTranslation();

  return (
    <div className="min-h-screen bg-white" dir={isRTL ? "rtl" : "ltr"}>
      {/* Hero Section */}

      <HeroSection />
      <AboutUs />
      <Logos />
      <Resources />
      <Events />
      <StayUpdated />
    </div>
  );
}
