"use client";

import { useTranslation } from "@/hooks/useTranslation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Image from "next/image";
import { useState } from "react";
import toast from "react-hot-toast";
import Hero from "@/components/successStories/Hero";

export default function DigitalSovereigntyPage() {
  const { t, isRTL } = useTranslation();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    toast.success(isRTL ? "تم إرسال الرسالة بنجاح" : "Message sent successfully");
    setFormData({ name: "", email: "" });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <div className="min-h-screen bg-white" dir={isRTL ? "rtl" : "ltr"}>
      {/* Hero Section */}
      <Hero/>
      {/* Platforms Section */}
      <section className="py-16 px-6 bg-gray-50">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t.digitalSovereignty.platformsTitle}
            </h2>
            <p className="text-gray-600 text-lg">
              {t.digitalSovereignty.platformsSubtitle}
            </p>
          </div>

          {/* Company Cards */}
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Vezeeta Card */}
            <Card className="bg-white hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="bg-blue-600 text-white text-2xl font-bold py-8 rounded-lg mb-4">
                  Vezeeta
                </div>
                <CardTitle className="text-xl">{t.digitalSovereignty.vezeeta.title}</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-gray-600 mb-6 leading-relaxed">
                  {t.digitalSovereignty.vezeeta.description}
                </CardDescription>
                <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-8">
                  {t.digitalSovereignty.vezeeta.buttonText}
                </Button>
              </CardContent>
            </Card>

            {/* Noon Card */}
            <Card className="bg-white hover:shadow-lg transition-shadow">
              <CardHeader className="text-center">
                <div className="bg-yellow-400 text-black text-2xl font-bold py-8 rounded-lg mb-4 flex items-center justify-center">
                  <span>noon</span>
                  <div className="ml-2 w-6 h-6 bg-yellow-600 rounded-full"></div>
                </div>
                <CardTitle className="text-xl">{t.digitalSovereignty.noon.title}</CardTitle>
              </CardHeader>
              <CardContent className="text-center">
                <CardDescription className="text-gray-600 mb-6 leading-relaxed">
                  {t.digitalSovereignty.noon.description}
                </CardDescription>
                <Button className="bg-yellow-500 hover:bg-yellow-600 text-black rounded-full px-8">
                  {t.digitalSovereignty.noon.buttonText}
                </Button>
              </CardContent>
            </Card>
          </div>

          {/* Center Button */}
          <div className="text-center">
            <Button className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-12 py-3">
              {isRTL ? "اكتشف المزيد" : "Discover More"}
            </Button>
          </div>
        </div>
      </section>

      {/* How We Help Section */}
      <section className="py-16 px-6 bg-white">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t.digitalSovereignty.howWeHelpTitle}
            </h2>
            <p className="text-gray-600">
              {t.digitalSovereignty.howWeHelpSubtitle}
            </p>
          </div>

          {/* Contact Form */}
          <Card className="bg-gray-50">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl">{t.digitalSovereignty.contactForm.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="grid md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t.digitalSovereignty.contactForm.nameLabel}
                    </label>
                    <input
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      placeholder={t.digitalSovereignty.contactForm.namePlaceholder}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {t.digitalSovereignty.contactForm.emailLabel}
                    </label>
                    <input
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      placeholder={t.digitalSovereignty.contactForm.emailPlaceholder}
                      className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      required
                    />
                  </div>
                </div>
                <div className="text-center">
                  <Button 
                    type="submit"
                    className="bg-blue-600 hover:bg-blue-700 text-white rounded-full px-12 py-3"
                  >
                    {t.digitalSovereignty.contactForm.submitButton}
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Stay Connected Section */}
      <section className="py-16 px-6 bg-blue-900 text-white">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-4">
            {t.digitalSovereignty.stayConnectedTitle}
          </h2>
          <p className="text-blue-100 mb-8 leading-relaxed">
            {t.digitalSovereignty.stayConnectedSubtitle}
          </p>
          
          {/* Newsletter Signup */}
          <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
            <form className="space-y-4">
              <input
                type="email"
                placeholder={t.digitalSovereignty.contactForm.emailPlaceholder}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg py-3">
                {isRTL ? "اشترك الآن" : "Subscribe Now"}
              </Button>
            </form>
          </div>
        </div>
      </section>
    </div>
  );
}
