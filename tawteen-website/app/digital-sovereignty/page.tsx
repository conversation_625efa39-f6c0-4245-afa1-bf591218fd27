"use client";

import { useTranslation } from "@/hooks/useTranslation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import toast from "react-hot-toast";
import VconnctStats from "@/components/successStories/VconnctStats";
import CompanyCards from "@/components/successStories/CompanyCards";
import Hero from "@/components/successStories/Hero";
import ContactForm2 from "@/components/contact us/ContactForm";

export default function DigitalSovereigntyPage() {
  const { t, isRTL } = useTranslation();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle form submission
    toast.success(isRTL ? "تم إرسال الرسالة بنجاح" : "Message sent successfully");
    setFormData({ name: "", email: "" });
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value,
    });
  };

  return (
    <div className="min-h-screen bg-white" dir={isRTL ? "rtl" : "ltr"}>
      <Hero/>

      {/* VconnctStats Section */}
      <VconnctStats />

      {/* Company Cards Section */}
      <CompanyCards />

      {/* How We Help Section */}
      <section className="py-16 px-6 bg-white">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              {t.digitalSovereignty.howWeHelpTitle}
            </h2>
            <p className="text-gray-600">
              {t.digitalSovereignty.howWeHelpSubtitle}
            </p>
          </div>
          </div>
          </section>

          {/* Contact Form */}
          <ContactForm2/>

      {/* Stay Connected Section */}
      <section className="py-16 px-6 bg-blue-900 text-white">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-4">
            {t.digitalSovereignty.stayConnectedTitle}
          </h2>
          <p className="text-blue-100 mb-8 leading-relaxed">
            {t.digitalSovereignty.stayConnectedSubtitle}
          </p>
          
          {/* Newsletter Signup */}
          <div className="bg-white rounded-lg p-6 max-w-md mx-auto">
            <form className="space-y-4">
              <input
                type="email"
                placeholder={t.digitalSovereignty.contactForm.emailPlaceholder}
                className="w-full px-4 py-3 border border-gray-300 rounded-lg text-gray-900 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <Button className="w-full bg-blue-600 hover:bg-blue-700 text-white rounded-lg py-3">
                {isRTL ? "اشترك الآن" : "Subscribe Now"}
              </Button>
            </form>
          </div>
        </div>
      </section>
    </div>
  );
}
