workflow:
  rules:
    # Only run the pipeline after the merge request is merged or for direct pushes
    - if: '$CI_PIPELINE_SOURCE == "push"'
      when: always
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $CI_MERGE_REQUEST_STATE == "merged"'
      when: always
    # Skip the pipeline for other merge request events 
    - when: never


image: docker:latest  
  
services:
  - docker:dind
variables:

  DOCKER_HOST: tcp://docker:2375 
  DOCKER_TLS_CERTDIR: ""     
 

stages:
  - build-dev
  - deploy-dev




# Stage: Build for Dev Branch
build-dev:
  stage: build-dev
  tags: 
    - docker


  script:
    - export IMAGE_NAME=$CI_REGISTRY_IMAGE
    - export IMAGE_TAG=$CI_JOB_ID
    - echo "**************************" | docker login  -u ghada --password-stdin  registry.vconnct.cloud
    - docker build -t $IMAGE_NAME:$IMAGE_TAG  .
    - docker push $IMAGE_NAME:$IMAGE_TAG
    - echo $CI_JOB_ID > build_job_id.txt
  artifacts: 
    paths:
      - build_job_id.txt
  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" && $CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_COMMIT_BRANCH == "dev" && $CI_MERGE_REQUEST_STATE == "merged"'



# Stage: Deploy for Dev Branch
deploy-dev:
  stage: deploy-dev
  needs:
    - build-dev
  tags: 
    - uat_apps

  script:

    - export IMAGE_NAME=$CI_REGISTRY_IMAGE
    - export IMAGE_TAG=$(cat build_job_id.txt)
    - kubectl create namespace tawteen-website || true
    - kubectl create secret docker-registry tawteen-website-secret --docker-server=registry.vconnct.cloud --docker-username=asd --docker-password=gldt-d9wCEb6aCjuszDMXiUmD --docker-email=<EMAIL> --namespace=tawteen-website || true
    
    - for i in ./.kubernetes/dev/*.yaml; do  
        envsubst < "$i" | kubectl apply -f - --wait; 
      done

  rules:
    - if: '$CI_COMMIT_BRANCH == "dev" && $CI_PIPELINE_SOURCE == "push"'
    - if: '$CI_COMMIT_BRANCH == "dev" && $CI_MERGE_REQUEST_STATE == "merged"'

